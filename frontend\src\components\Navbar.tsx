import { useTranslation } from 'react-i18next';
import { ThemeToggle } from './ThemeToggle';
import { LanguageSelector } from './LanguageSelector';
import { useAuthStore, useAppStore } from '../lib/store';
import { apiClient } from '../lib/api';

export function Navbar() {
  const { t } = useTranslation();
  const { user, logout } = useAuthStore();
  const { toggleSidebar } = useAppStore();

  const handleLogout = async () => {
    try {
      await apiClient.logout();
      logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Hata olsa bile kullanıcıyı çıkış yapmış sayalım
      logout();
    }
  };

  return (
    <div className="navbar bg-base-100 shadow-md">
      <div className="flex-none">
        <button className="btn btn-square btn-ghost" onClick={toggleSidebar}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            className="inline-block w-5 h-5 stroke-current"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>
      <div className="flex-1">
        <a className="btn btn-ghost text-xl">Atropos POS</a>
      </div>
      <div className="flex-none gap-2">
        <ThemeToggle />
        <LanguageSelector />
        <div className="dropdown dropdown-end">
          <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
            <div className="w-10 rounded-full">
              <img
                alt="User Avatar"
                src={user?.avatar || 'https://ui-avatars.com/api/?name=' + user?.firstName + '+' + user?.lastName}
              />
            </div>
          </div>
          <ul
            tabIndex={0}
            className="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52"
          >
            <li>
              <a className="justify-between">
                {t('navigation.profile')}
                <span className="badge">{t('common.new')}</span>
              </a>
            </li>
            <li>
              <a>{t('navigation.settings')}</a>
            </li>
            <li>
              <a onClick={handleLogout}>{t('auth.logout')}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
