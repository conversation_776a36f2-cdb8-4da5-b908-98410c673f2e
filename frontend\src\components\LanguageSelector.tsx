import { useTranslation } from 'react-i18next';
import { Language } from '@atropos/shared';
import { useSettingsStore } from '../lib/store';

export function LanguageSelector() {
  const { t, i18n } = useTranslation();
  const { language, setLanguage } = useSettingsStore();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    i18n.changeLanguage(newLanguage);
  };

  return (
    <div className="dropdown dropdown-end">
      <div tabIndex={0} role="button" className="btn btn-ghost btn-circle">
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
      </div>
      <ul
        tabIndex={0}
        className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"
      >
        <li>
          <a
            onClick={() => handleLanguageChange(Language.TR)}
            className={language === Language.TR ? 'active' : ''}
          >
            🇹🇷 {t('language.turkish')}
          </a>
        </li>
        <li>
          <a
            onClick={() => handleLanguageChange(Language.EN)}
            className={language === Language.EN ? 'active' : ''}
          >
            🇺🇸 {t('language.english')}
          </a>
        </li>
      </ul>
    </div>
  );
}
