import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';

export function Dashboard() {
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Kullanıcı giriş yapmış mı kontrol et
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }
    setUser(JSON.parse(userData));
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/login');
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navbar */}
      <div className="bg-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-800">Atropos POS</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <button
                  className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                  onClick={() => {
                    const dropdown = document.getElementById('user-dropdown');
                    dropdown?.classList.toggle('hidden');
                  }}
                >
                  <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  <span>{user.username}</span>
                </button>
                <div id="user-dropdown" className="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profil</a>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Ayarlar</a>
                  <a href="#" onClick={handleLogout} className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Çıkış Yap</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Hoş Geldiniz, {user.username}!</h1>
          <p className="text-gray-600">Atropos POS Sistemi başarıyla çalışıyor.</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Bugünkü Siparişler</p>
                <p className="text-2xl font-semibold text-gray-800">25</p>
              </div>
              <div className="text-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-8 h-8 stroke-current">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2">%12 artış</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Günlük Ciro</p>
                <p className="text-2xl font-semibold text-gray-800">₺2,450</p>
              </div>
              <div className="text-orange-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-8 h-8 stroke-current">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2">%8 artış</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Aktif Masalar</p>
                <p className="text-2xl font-semibold text-gray-800">8/12</p>
              </div>
              <div className="text-green-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-8 h-8 stroke-current">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                </svg>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-2">%67 doluluk</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Bekleyen Siparişler</p>
                <p className="text-2xl font-semibold text-gray-800">3</p>
              </div>
              <div className="text-yellow-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-8 h-8 stroke-current">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-2">Mutfakta</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">🍽️ Yeni Sipariş</h2>
              <p>Hızlı sipariş oluştur</p>
              <div className="card-actions justify-end">
                <button className="btn btn-primary">Başlat</button>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">🪑 Masa Durumu</h2>
              <p>Masa düzenini görüntüle</p>
              <div className="card-actions justify-end">
                <button className="btn btn-secondary">Görüntüle</button>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">📊 Raporlar</h2>
              <p>Satış raporlarını incele</p>
              <div className="card-actions justify-end">
                <button className="btn btn-accent">Raporlar</button>
              </div>
            </div>
          </div>
        </div>

        {/* Test Bilgileri */}
        <div className="mt-8 p-4 bg-info/10 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">🎉 Kurulum Başarılı!</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium">Frontend Teknolojileri:</h4>
              <ul className="list-disc list-inside ml-4">
                <li>React 18 + TypeScript ✅</li>
                <li>Vite ✅</li>
                <li>Tailwind CSS + DaisyUI ✅</li>
                <li>React Router ✅</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium">Backend Teknolojileri:</h4>
              <ul className="list-disc list-inside ml-4">
                <li>Node.js + Fastify ✅</li>
                <li>PostgreSQL + Prisma ✅</li>
                <li>JWT Authentication ✅</li>
                <li>Socket.io ✅</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
