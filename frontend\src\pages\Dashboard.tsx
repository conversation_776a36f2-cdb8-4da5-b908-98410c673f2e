import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';

export function Dashboard() {
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Kullanıcı giriş yapmış mı kontrol et
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }
    setUser(JSON.parse(userData));
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/login');
  };

  if (!user) {
    return <div className="loading loading-spinner loading-lg"></div>;
  }

  return (
    <div className="min-h-screen bg-base-200">
      {/* Navbar */}
      <div className="navbar bg-base-100 shadow-lg">
        <div className="flex-1">
          <a className="btn btn-ghost text-xl">Atropos POS</a>
        </div>
        <div className="flex-none gap-2">
          <div className="dropdown dropdown-end">
            <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
              <div className="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
                {user.username.charAt(0).toUpperCase()}
              </div>
            </div>
            <ul tabIndex={0} className="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
              <li><a>Profil</a></li>
              <li><a>Ayarlar</a></li>
              <li><a onClick={handleLogout}>Çıkış Yap</a></li>
            </ul>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Hoş Geldiniz, {user.username}!</h1>
          <p className="text-base-content/70">Atropos POS Sistemi başarıyla çalışıyor.</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-figure text-primary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-8 h-8 stroke-current">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div className="stat-title">Bugünkü Siparişler</div>
            <div className="stat-value">25</div>
            <div className="stat-desc">%12 artış</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-figure text-secondary">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-8 h-8 stroke-current">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
              </svg>
            </div>
            <div className="stat-title">Günlük Ciro</div>
            <div className="stat-value">₺2,450</div>
            <div className="stat-desc">%8 artış</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-figure text-accent">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-8 h-8 stroke-current">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
              </svg>
            </div>
            <div className="stat-title">Aktif Masalar</div>
            <div className="stat-value">8/12</div>
            <div className="stat-desc">%67 doluluk</div>
          </div>

          <div className="stat bg-base-100 shadow rounded-lg">
            <div className="stat-figure text-warning">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-8 h-8 stroke-current">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div className="stat-title">Bekleyen Siparişler</div>
            <div className="stat-value">3</div>
            <div className="stat-desc">Mutfakta</div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">🍽️ Yeni Sipariş</h2>
              <p>Hızlı sipariş oluştur</p>
              <div className="card-actions justify-end">
                <button className="btn btn-primary">Başlat</button>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">🪑 Masa Durumu</h2>
              <p>Masa düzenini görüntüle</p>
              <div className="card-actions justify-end">
                <button className="btn btn-secondary">Görüntüle</button>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">📊 Raporlar</h2>
              <p>Satış raporlarını incele</p>
              <div className="card-actions justify-end">
                <button className="btn btn-accent">Raporlar</button>
              </div>
            </div>
          </div>
        </div>

        {/* Test Bilgileri */}
        <div className="mt-8 p-4 bg-info/10 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">🎉 Kurulum Başarılı!</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium">Frontend Teknolojileri:</h4>
              <ul className="list-disc list-inside ml-4">
                <li>React 18 + TypeScript ✅</li>
                <li>Vite ✅</li>
                <li>Tailwind CSS + DaisyUI ✅</li>
                <li>React Router ✅</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium">Backend Teknolojileri:</h4>
              <ul className="list-disc list-inside ml-4">
                <li>Node.js + Fastify ✅</li>
                <li>PostgreSQL + Prisma ✅</li>
                <li>JWT Authentication ✅</li>
                <li>Socket.io ✅</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
