import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export function Login() {
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();

    // Basit doğrulama
    if (username === 'admin' && password === 'admin123') {
      // Gerçek uygulamada API'ye istek atılır
      localStorage.setItem('user', JSON.stringify({ username, role: 'admin' }));
      navigate('/dashboard');
    } else {
      setError('Kullanıcı adı veya şifre hatalı');
    }
  };

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center">
      <div className="card w-96 bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title text-center text-2xl font-bold mb-6">Atropos POS</h2>

          {error && (
            <div className="alert alert-error mb-4">
              <span>{error}</span>
            </div>
          )}

          <form onSubmit={handleLogin}>
            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">Kullanıcı Adı</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div className="form-control mb-6">
              <label className="label">
                <span className="label-text">Şifre</span>
              </label>
              <input
                type="password"
                className="input input-bordered"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="form-control">
              <button className="btn btn-primary">Giriş Yap</button>
            </div>
          </form>

          <div className="divider mt-6">Demo Bilgileri</div>
          <div className="text-sm text-center">
            <p>Kullanıcı Adı: admin</p>
            <p>Şifre: admin123</p>
          </div>
        </div>
      </div>
    </div>
  );
}
