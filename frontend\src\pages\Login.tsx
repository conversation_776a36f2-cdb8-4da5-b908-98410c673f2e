import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export function Login() {
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();

    // Basit doğrulama
    if (username === 'admin' && password === 'admin123') {
      // Gerçek uygulamada API'ye istek atılır
      localStorage.setItem('user', JSON.stringify({ username, role: 'admin' }));
      navigate('/dashboard');
    } else {
      setError('Kullanıcı adı veya şifre hatalı');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="w-96 bg-white shadow-xl rounded-lg">
        <div className="p-8">
          <h2 className="text-center text-2xl font-bold mb-6 text-gray-800">Atropos POS</h2>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <span>{error}</span>
            </div>
          )}

          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Kullanıcı Adı
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Şifre
              </label>
              <input
                type="password"
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="mb-6">
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                Giriş Yap
              </button>
            </div>
          </form>

          <div className="border-t pt-6 mt-6">
            <div className="text-sm text-center text-gray-600">
              <p className="font-semibold mb-2">Demo Bilgileri</p>
              <p>Kullanıcı Adı: admin</p>
              <p>Şifre: admin123</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
