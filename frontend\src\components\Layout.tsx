import { ReactNode } from 'react';
import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';
import { useAppStore } from '../lib/store';

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { sidebarOpen } = useAppStore();

  return (
    <div className="drawer lg:drawer-open">
      <input id="drawer-toggle" type="checkbox" className="drawer-toggle" checked={sidebarOpen} readOnly />
      <div className="drawer-content flex flex-col">
        <Navbar />
        <main className="flex-1 p-4 overflow-auto bg-base-200">
          {children}
        </main>
      </div>
      <Sidebar />
    </div>
  );
}
