import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { useAppStore } from '../lib/store';

interface MenuItem {
  path: string;
  icon: string;
  label: string;
  badge?: string;
}

export function Sidebar() {
  const { t } = useTranslation();
  const location = useLocation();
  const { sidebarOpen } = useAppStore();

  const menuItems: MenuItem[] = [
    {
      path: '/',
      icon: '🏠',
      label: t('navigation.dashboard'),
    },
    {
      path: '/orders',
      icon: '📋',
      label: t('navigation.orders'),
      badge: '3',
    },
    {
      path: '/tables',
      icon: '🪑',
      label: t('navigation.tables'),
    },
    {
      path: '/products',
      icon: '🍽️',
      label: t('navigation.products'),
    },
    {
      path: '/categories',
      icon: '📂',
      label: t('navigation.categories'),
    },
    {
      path: '/customers',
      icon: '👥',
      label: t('navigation.customers'),
    },
    {
      path: '/kitchen',
      icon: '👨‍🍳',
      label: t('navigation.kitchen'),
    },
    {
      path: '/cashier',
      icon: '💰',
      label: t('navigation.cashier'),
    },
    {
      path: '/reports',
      icon: '📊',
      label: t('navigation.reports'),
    },
    {
      path: '/settings',
      icon: '⚙️',
      label: t('navigation.settings'),
    },
  ];

  if (!sidebarOpen) {
    return null;
  }

  return (
    <div className="drawer-side">
      <label htmlFor="drawer-toggle" className="drawer-overlay"></label>
      <aside className="w-64 min-h-full bg-base-200">
        <div className="p-4">
          <h2 className="text-lg font-semibold text-base-content">
            Atropos POS
          </h2>
        </div>
        <ul className="menu p-4 space-y-2">
          {menuItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                className={`flex items-center space-x-3 ${
                  location.pathname === item.path
                    ? 'active bg-primary text-primary-content'
                    : 'hover:bg-base-300'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <span className="flex-1">{item.label}</span>
                {item.badge && (
                  <span className="badge badge-primary badge-sm">
                    {item.badge}
                  </span>
                )}
              </Link>
            </li>
          ))}
        </ul>
      </aside>
    </div>
  );
}
