@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Atropos POS */
:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
}

/* Custom component styles */
.pos-card {
  @apply bg-white shadow-lg rounded-lg p-4 border border-gray-300;
}

.pos-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium px-4 py-2 rounded;
}

.pos-input {
  @apply border border-gray-300 rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }
  .printable, .printable * {
    visibility: visible;
  }
  .printable {
    position: absolute;
    left: 0;
    top: 0;
  }
}
